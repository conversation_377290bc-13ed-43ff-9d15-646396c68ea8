/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_LATTE_ART_CONFIG_H
#define AUBO_COFFEE_SERVICE_LATTE_ART_CONFIG_H

#include <string>
#include <vector>
#include <memory>
#include "../core/coffee_order.h"

namespace aubo {

/**
 * @struct MotionParameters
 * @brief 运动参数结构
 */
struct MotionParameters {
    double line_acceleration;      ///< 线性加速度 (m/s²)
    double angle_acceleration;     ///< 角度加速度 (rad/s²)
    double line_velocity;          ///< 线性速度 (m/s)
    double angle_velocity;         ///< 角度速度 (rad/s)
    double blend_radius;           ///< 混合半径 (m)

    MotionParameters() 
        : line_acceleration(0.436332)
        , angle_acceleration(2.0)
        , line_velocity(0.436332)
        , angle_velocity(2.0)
        , blend_radius(0.01) {}
};

/**
 * @struct LatteArtTrajectory
 * @brief 拉花轨迹数据结构
 */
struct LatteArtTrajectory {
    std::string name;                                    ///< 拉花名称
    std::string description;                             ///< 拉花描述
    MotionParameters motion_params;                      ///< 运动参数
    std::vector<std::vector<double>> left_waypoints;     ///< 左臂路径点
    std::vector<std::vector<double>> right_waypoints;    ///< 右臂路径点
};

/**
 * @class LatteArtConfig
 * @brief 拉花配置管理类
 * 
 * 负责加载和管理拉花轨迹配置文件
 */
class LatteArtConfig {
public:
    /**
     * @brief 构造函数
     */
    LatteArtConfig();

    /**
     * @brief 析构函数
     */
    ~LatteArtConfig();

    /**
     * @brief 从目录加载拉花配置
     * @param config_dir_path 配置目录路径
     * @return 加载成功返回true
     */
    bool load_from_directory(const std::string& config_dir_path);

    /**
     * @brief 检查配置是否已加载
     * @return 已加载返回true
     */
    bool is_loaded() const;

    /**
     * @brief 获取左臂机器人路径点
     * @param art_type 拉花类型
     * @return 路径点列表
     */
    std::vector<std::vector<double>> get_left_robot_waypoints(LatteArtType art_type) const;

    /**
     * @brief 获取右臂机器人路径点
     * @param art_type 拉花类型
     * @return 路径点列表
     */
    std::vector<std::vector<double>> get_right_robot_waypoints(LatteArtType art_type) const;

    /**
     * @brief 获取运动参数
     * @param art_type 拉花类型
     * @return 运动参数
     */
    MotionParameters get_motion_parameters(LatteArtType art_type) const;

    /**
     * @brief 获取拉花轨迹
     * @param art_type 拉花类型
     * @return 拉花轨迹数据
     */
    LatteArtTrajectory get_trajectory(LatteArtType art_type) const;

private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_LATTE_ART_CONFIG_H
