/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#include "coffee_workflow.h"

#include <atomic>
#include <chrono>
#include <fstream>
#include <future>
#include <thread>

#include <aubo-base/log.h>
#include <nlohmann/json.hpp>
#include <taskflow/taskflow.hpp>

#include "../robots/left_robot/left_robot.h"
#include "../robots/right_robot/right_robot.h"
#include "../devices/cup_dispenser/cup_dispenser.h"
#include "../devices/coffee_machine/coffee_machine.h"
#include "../devices/milk_container_cleaner/milk_container_cleaner.h"

namespace aubo {

using json = nlohmann::json;

class CoffeeWorkflowEngine::Impl {
public:
    Impl(std::shared_ptr<LeftRobot> left_robot,
         std::shared_ptr<RightRobot> right_robot,
         std::shared_ptr<CupDispenser> cup_dispenser,
         std::shared_ptr<CoffeeMachine> coffee_machine,
         std::shared_ptr<MilkContainerCleaner> milk_container_cleaner)
        : left_robot_(left_robot), right_robot_(right_robot),
          cup_dispenser_(cup_dispenser), coffee_machine_(coffee_machine),
          milk_container_cleaner_(milk_container_cleaner),
          is_executing_(false), stop_requested_(false) {}



    bool load_workflows_from_directory(const std::string& workflow_directory) {
        LOG_INFO("[WorkflowEngine] 从目录加载工作流: {}", workflow_directory);

        workflows_.clear();
        int loaded_count = 0;

        try {
            // 这里应该遍历目录中的所有JSON文件
            // 为了简化，我们手动加载已知的工作流文件
            std::vector<std::string> workflow_files = {
                "americano.json",
                "latte_heart.json",
                "latte_leaf.json",
                "latte_tulip.json",
                "latte_swan.json",
                "cappuccino_heart.json"
            };

            for (const auto& filename : workflow_files) {
                std::string filepath = workflow_directory + "/" + filename;
                if (load_single_workflow(filepath)) {
                    loaded_count++;
                }
            }

            LOG_INFO("[WorkflowEngine] 从目录成功加载 {} 个工作流", loaded_count);
            return loaded_count > 0;

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 从目录加载工作流失败: {}", e.what());
            return false;
        }
    }



    bool load_single_workflow(const std::string& workflow_file_path) {
        try {
            std::ifstream file(workflow_file_path);
            if (!file.is_open()) {
                LOG_WARN("[WorkflowEngine] 无法打开工作流文件: {}", workflow_file_path);
                return false;
            }

            json workflow_json;
            file >> workflow_json;

            if (workflow_json.contains("workflow")) {
                const auto& workflow_data = workflow_json["workflow"];

                // 从文件名提取工作流ID
                std::string filename = workflow_file_path.substr(workflow_file_path.find_last_of("/") + 1);
                std::string workflow_id = filename.substr(0, filename.find_last_of("."));

                CoffeeWorkflow workflow = parse_single_workflow(workflow_data);
                workflows_[workflow_id] = workflow;

                LOG_INFO("[WorkflowEngine] 加载单个工作流: {} - {}", workflow_id, workflow.description);
                return true;
            }

            return false;

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 加载单个工作流失败: {} - {}", workflow_file_path, e.what());
            return false;
        }
    }

    std::vector<std::string> get_available_workflows() const {
        std::vector<std::string> names;
        for (const auto& [name, workflow] : workflows_) {
            names.push_back(name);
        }
        return names;
    }

    CoffeeWorkflow get_workflow(const std::string& workflow_name) const {
        auto it = workflows_.find(workflow_name);
        if (it != workflows_.end()) {
            return it->second;
        }
        return CoffeeWorkflow();
    }

    bool execute_workflow(const std::string& workflow_name, const CoffeeOrder& order) {
        auto it = workflows_.find(workflow_name);
        if (it == workflows_.end()) {
            LOG_ERROR("[WorkflowEngine] 工作流不存在: {}", workflow_name);
            return false;
        }
        return execute_workflow(it->second, order);
    }

    bool execute_workflow(const CoffeeWorkflow& workflow, const CoffeeOrder& order) {
        if (is_executing_) {
            LOG_ERROR("[WorkflowEngine] 已有工作流正在执行");
            return false;
        }

        LOG_INFO("[WorkflowEngine] 开始执行工作流: {} - {}", workflow.name, workflow.description);
        
        is_executing_ = true;
        current_step_ = "";
        stop_requested_ = false;

        bool success = true;
        
        try {
            // 执行根步骤组
            current_step_ = workflow.root_steps.name;
            LOG_INFO("[WorkflowEngine] 执行工作流: {} - {}", workflow.name, workflow.root_steps.name);

            if (!execute_workflow_item(workflow.root_steps, order)) {
                LOG_ERROR("[WorkflowEngine] 工作流执行失败: {}", workflow.root_steps.name);
                success = false;
            } else {
                LOG_INFO("[WorkflowEngine] 工作流执行完成: {}", workflow.root_steps.name);
            }
            
        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 工作流执行异常: {}", e.what());
            success = false;
        }

        is_executing_ = false;
        current_step_ = "";
        
        if (stop_requested_) {
            LOG_WARN("[WorkflowEngine] 工作流执行被用户停止");
            return false;
        }
        
        if (success) {
            LOG_INFO("[WorkflowEngine] 工作流执行完成: {}", workflow.name);
        } else {
            LOG_ERROR("[WorkflowEngine] 工作流执行失败: {}", workflow.name);
        }
        
        return success;
    }

    bool stop_current_workflow() {
        if (!is_executing_) {
            LOG_WARN("[WorkflowEngine] 没有正在执行的工作流");
            return false;
        }
        
        LOG_INFO("[WorkflowEngine] 请求停止当前工作流");
        stop_requested_ = true;
        return true;
    }

    bool is_executing() const {
        return is_executing_;
    }

    std::string get_current_step() const {
        return current_step_;
    }

    void set_global_settings(const WorkflowSettings& settings) {
        settings_ = settings;
    }

    WorkflowSettings get_global_settings() const {
        return settings_;
    }

private:
    void load_global_settings(const json& settings_json) {
        if (settings_json.contains("default_timeout")) {
            settings_.default_timeout = settings_json["default_timeout"];
        }
        if (settings_json.contains("retry_attempts")) {
            settings_.retry_attempts = settings_json["retry_attempts"];
        }
        if (settings_json.contains("emergency_stop_on_failure")) {
            settings_.emergency_stop_on_failure = settings_json["emergency_stop_on_failure"];
        }
        if (settings_json.contains("parallel_execution_delay")) {
            settings_.parallel_execution_delay = settings_json["parallel_execution_delay"];
        }
    }



    CoffeeWorkflow parse_single_workflow(const json& workflow_json) {
        CoffeeWorkflow workflow;

        if (workflow_json.contains("name")) {
            workflow.name = workflow_json["name"];
        }
        if (workflow_json.contains("description")) {
            workflow.description = workflow_json["description"];
        }
        if (workflow_json.contains("coffee_type")) {
            workflow.coffee_type = workflow_json["coffee_type"];
        }

        // 解析 steps 作为根执行组
        if (workflow_json.contains("steps")) {
            workflow.root_steps = parse_workflow_item(workflow_json["steps"]);
            workflow.root_steps.type = ActionType::GROUP;  // 确保是 GROUP 类型
        }

        return workflow;
    }



    WorkflowItem parse_workflow_item(const json& item_json) {
        WorkflowItem item;

        // 解析类型
        if (item_json.contains("type")) {
            item.type = string_to_action_type(item_json["type"]);
        }

        // 解析名称
        if (item_json.contains("name")) {
            item.name = item_json["name"];
        }

        // 根据类型解析不同的字段
        switch (item.type) {
            case ActionType::ACTION:
                // 解析动作相关字段
                if (item_json.contains("executor")) {
                    item.executor = string_to_executor_type(item_json["executor"]);
                }
                if (item_json.contains("operation")) {
                    item.operation = item_json["operation"];
                }
                if (item_json.contains("parameters")) {
                    for (const auto& [key, value] : item_json["parameters"].items()) {
                        if (value.is_string()) {
                            item.parameters.set(key, value.get<std::string>());
                        } else if (value.is_number_integer()) {
                            item.parameters.set(key, value.get<int>());
                        } else if (value.is_number_float()) {
                            item.parameters.set(key, value.get<double>());
                        } else if (value.is_boolean()) {
                            item.parameters.set(key, value.get<bool>());
                        }
                    }
                }
                break;

            case ActionType::WAIT:
                // 解析等待时长
                if (item_json.contains("duration")) {
                    item.duration = item_json["duration"];
                }
                break;

            case ActionType::GROUP:
                // 解析执行模式
                if (item_json.contains("mode")) {
                    item.mode = string_to_execution_type(item_json["mode"]);
                } else if (item_json.contains("execution")) {
                    item.mode = string_to_execution_type(item_json["execution"]);
                }

                // 解析子项列表
                if (item_json.contains("items")) {
                    for (const auto& sub_item_json : item_json["items"]) {
                        WorkflowItem sub_item = parse_workflow_item(sub_item_json);
                        item.items.push_back(sub_item);
                    }
                } else if (item_json.contains("actions")) {
                    // 兼容旧格式
                    for (const auto& sub_item_json : item_json["actions"]) {
                        WorkflowItem sub_item = parse_workflow_item(sub_item_json);
                        item.items.push_back(sub_item);
                    }
                }
                break;
        }

        return item;
    }





    bool execute_items_sequential(const std::vector<WorkflowItem>& items, const CoffeeOrder& order) {
        for (const auto& item : items) {
            if (stop_requested_) return false;

            if (!execute_workflow_item(item, order)) {
                return false;
            }
        }
        return true;
    }

    bool execute_items_parallel(const std::vector<WorkflowItem>& items, const CoffeeOrder& order) {
        if (items.empty()) return true;

        tf::Executor executor;
        tf::Taskflow taskflow;

        std::vector<bool> results(items.size(), false);

        for (size_t i = 0; i < items.size(); ++i) {
            auto task = taskflow.emplace([this, &items, &results, &order, i]() {
                if (!stop_requested_) {
                    results[i] = execute_workflow_item(items[i], order);
                }
            });
            task.name(items[i].name.empty() ? items[i].operation : items[i].name);
        }

        executor.run(taskflow).wait();

        // 检查所有任务是否成功
        for (bool result : results) {
            if (!result) return false;
        }

        return true;
    }

    bool execute_workflow_item(const WorkflowItem& item, const CoffeeOrder& order) {
        switch (item.type) {
            case ActionType::ACTION:
                return execute_single_action(item, order);
            case ActionType::WAIT:
                return execute_wait_action(item);
            case ActionType::GROUP:
                return execute_group_action(item, order);
            default:
                LOG_ERROR("[WorkflowEngine] 未知项目类型");
                return false;
        }
    }

    bool execute_single_action(const WorkflowItem& item, const CoffeeOrder& order) {
        LOG_INFO("[WorkflowEngine] 执行动作: {} (执行器: {})",
                 item.operation, executor_type_to_string(item.executor));

        bool success = false;

        try {
            switch (item.executor) {
                case ExecutorType::LEFT_ARM:
                    success = execute_left_robot_action(item, order);
                    break;
                case ExecutorType::RIGHT_ARM:
                    success = execute_right_robot_action(item, order);
                    break;
                case ExecutorType::CUP_DISPENSER:
                    success = execute_cup_dispenser_action(item, order);
                    break;
                case ExecutorType::COFFEE_MACHINE:
                    success = execute_coffee_machine_action(item, order);
                    break;
                case ExecutorType::MILK_CONTAINER_CLEANER:
                    success = execute_milk_cleaner_action(item, order);
                    break;
                default:
                    LOG_ERROR("[WorkflowEngine] 未知执行器类型");
                    return false;
            }

            if (success) {
                LOG_INFO("[WorkflowEngine] 动作执行成功: {}", item.operation);
            } else {
                LOG_ERROR("[WorkflowEngine] 动作执行失败: {}", item.operation);
            }

        } catch (const std::exception& e) {
            LOG_ERROR("[WorkflowEngine] 动作执行异常: {} - {}", item.operation, e.what());
            success = false;
        }

        return success;
    }

    bool execute_wait_action(const WorkflowItem& item) {
        LOG_INFO("[WorkflowEngine] 等待 {} 秒", item.duration);
        std::this_thread::sleep_for(std::chrono::seconds(item.duration));
        return true;
    }

    bool execute_group_action(const WorkflowItem& item, const CoffeeOrder& order) {
        LOG_INFO("[WorkflowEngine] 执行组: {} (执行模式: {})",
                 item.name, execution_type_to_string(item.mode));

        if (item.mode == ExecutionType::SEQUENTIAL) {
            return execute_items_sequential(item.items, order);
        } else {
            return execute_items_parallel(item.items, order);
        }
    }

    bool execute_left_robot_action(const WorkflowItem& item, const CoffeeOrder& order) {
        if (!left_robot_) {
            LOG_ERROR("[WorkflowEngine] 左臂机器人未初始化");
            return false;
        }

        if (item.operation == "move_to_ready") {
            return left_robot_->move_to_ready();
        } else if (item.operation == "move_to_cup_outlet") {
            return left_robot_->move_to_cup_outlet();
        } else if (item.operation == "move_to_coffee_outlet") {
            return left_robot_->move_to_coffee_outlet();
        } else if (item.operation == "move_to_latte_art") {
            return left_robot_->move_to_latte_art();
        } else if (item.operation == "do_latte_art") {
            LatteArtType art_type = order.latte_art;
            if (item.parameters.has("art_type")) {
                std::string art_type_str = item.parameters.get<std::string>("art_type");
                // 将字符串转换为拉花类型
                if (art_type_str == "heart") art_type = LatteArtType::HEART;
                else if (art_type_str == "leaf") art_type = LatteArtType::LEAF;
                else if (art_type_str == "tulip") art_type = LatteArtType::TULIP;
                else if (art_type_str == "swan") art_type = LatteArtType::SWAN;
                else if (art_type_str == "none") art_type = LatteArtType::NONE;
            }
            return left_robot_->do_latte_art(art_type);
        } else if (item.operation == "deliver_coffee") {
            return left_robot_->deliver_coffee();
        } else if (item.operation == "move_to_home") {
            return left_robot_->move_to_home();
        } else {
            LOG_ERROR("[WorkflowEngine] 未知的左臂动作: {}", item.operation);
            return false;
        }
    }

    bool execute_right_robot_action(const WorkflowAction& action, const CoffeeOrder& order) {
        if (!right_robot_) {
            LOG_ERROR("[WorkflowEngine] 右臂机器人未初始化");
            return false;
        }

        if (action.operation == "move_to_ready") {
            return right_robot_->move_to_ready();
        } else if (action.operation == "move_to_milk_outlet") {
            return right_robot_->move_to_milk_outlet();
        } else if (action.operation == "get_milk") {
            return right_robot_->get_milk();
        } else if (action.operation == "shake_milk") {
            // 支持参数化的摇奶操作
            int shake_rounds = 10; // 默认值
            if (action.parameters.has("shake_rounds")) {
                shake_rounds = action.parameters.get<int>("shake_rounds");
            }
            return right_robot_->shake_milk(shake_rounds);
        } else if (action.operation == "move_to_latte_art") {
            return right_robot_->move_to_latte_art();
        } else if (action.operation == "do_latte_art") {
            LatteArtType art_type = order.latte_art;
            if (action.parameters.has("art_type")) {
                std::string art_type_str = action.parameters.get<std::string>("art_type");
                // 将字符串转换为拉花类型
                if (art_type_str == "heart") art_type = LatteArtType::HEART;
                else if (art_type_str == "leaf") art_type = LatteArtType::LEAF;
                else if (art_type_str == "tulip") art_type = LatteArtType::TULIP;
                else if (art_type_str == "swan") art_type = LatteArtType::SWAN;
                else if (art_type_str == "none") art_type = LatteArtType::NONE;
            }
            return right_robot_->do_latte_art(art_type);
        } else if (action.operation == "move_to_pour_milk") {
            return right_robot_->move_to_pour_milk();
        } else if (action.operation == "pour_remaining_milk") {
            return right_robot_->pour_remaining_milk();
        } else if (action.operation == "move_to_cleaning") {
            return right_robot_->move_to_cleaning();
        } else if (action.operation == "start_cleaning") {
            return right_robot_->start_cleaning();
        } else if (action.operation == "stop_cleaning") {
            return right_robot_->stop_cleaning();
        } else if (action.operation == "move_to_home") {
            return right_robot_->move_to_home();
        } else {
            LOG_ERROR("[WorkflowEngine] 未知的右臂动作: {}", action.operation);
            return false;
        }
    }

    bool execute_cup_dispenser_action(const WorkflowAction& action, const CoffeeOrder& /* order */) {
        if (!cup_dispenser_) {
            LOG_ERROR("[WorkflowEngine] 杯子分配器未初始化");
            return false;
        }

        if (action.operation == "dispense_cup") {
            return cup_dispenser_->dispense_cup();
        } else {
            LOG_ERROR("[WorkflowEngine] 未知的杯子分配器动作: {}", action.operation);
            return false;
        }
    }

    bool execute_coffee_machine_action(const WorkflowAction& action, const CoffeeOrder& /* order */) {
        if (!coffee_machine_) {
            LOG_ERROR("[WorkflowEngine] 咖啡机未初始化");
            return false;
        }

        if (action.operation == "brew_coffee") {
            // 根据订单设置咖啡参数
            CoffeeParameters params;
            // 默认使用中杯，可以通过参数覆盖
            params.size = CoffeeSize::MEDIUM;

            // 检查动作参数中是否指定了大小
            if (action.parameters.has("size")) {
                std::string size_str = action.parameters.get<std::string>("size");
                params.size = string_to_coffee_size(size_str);
            }

            return coffee_machine_->brew_coffee(params);
        } else if (action.operation == "dispense_milk") {
            // 分配牛奶
            return coffee_machine_->dispense_milk();
        } else {
            LOG_ERROR("[WorkflowEngine] 未知的咖啡机动作: {}", action.operation);
            return false;
        }
    }

    bool execute_milk_cleaner_action(const WorkflowAction& action, const CoffeeOrder& /* order */) {
        if (!milk_container_cleaner_) {
            LOG_ERROR("[WorkflowEngine] 牛奶容器清洁器未初始化");
            return false;
        }

        if (action.operation == "start_cleaning") {
            CleaningParameters params;
            return milk_container_cleaner_->start_cleaning(params);
        } else if (action.operation == "stop_cleaning") {
            return milk_container_cleaner_->stop_cleaning();
        } else {
            LOG_ERROR("[WorkflowEngine] 未知的牛奶容器清洁器动作: {}", action.operation);
            return false;
        }
    }

    std::shared_ptr<LeftRobot> left_robot_;
    std::shared_ptr<RightRobot> right_robot_;
    std::shared_ptr<CupDispenser> cup_dispenser_;
    std::shared_ptr<CoffeeMachine> coffee_machine_;
    std::shared_ptr<MilkContainerCleaner> milk_container_cleaner_;

    std::map<std::string, CoffeeWorkflow> workflows_;
    WorkflowSettings settings_;

    std::atomic<bool> is_executing_;
    std::atomic<bool> stop_requested_;
    std::string current_step_;
};

// CoffeeWorkflowEngine 公共接口实现
CoffeeWorkflowEngine::CoffeeWorkflowEngine(std::shared_ptr<LeftRobot> left_robot,
                                          std::shared_ptr<RightRobot> right_robot,
                                          std::shared_ptr<CupDispenser> cup_dispenser,
                                          std::shared_ptr<CoffeeMachine> coffee_machine,
                                          std::shared_ptr<MilkContainerCleaner> milk_container_cleaner) {
    impl_ = std::make_unique<Impl>(left_robot, right_robot, cup_dispenser, coffee_machine, milk_container_cleaner);
}

CoffeeWorkflowEngine::~CoffeeWorkflowEngine() = default;

bool CoffeeWorkflowEngine::load_workflows_from_directory(const std::string& workflow_directory) {
    return impl_->load_workflows_from_directory(workflow_directory);
}

bool CoffeeWorkflowEngine::load_single_workflow(const std::string& workflow_file_path) {
    return impl_->load_single_workflow(workflow_file_path);
}

std::vector<std::string> CoffeeWorkflowEngine::get_available_workflows() const {
    return impl_->get_available_workflows();
}

CoffeeWorkflow CoffeeWorkflowEngine::get_workflow(const std::string& workflow_name) const {
    return impl_->get_workflow(workflow_name);
}

bool CoffeeWorkflowEngine::execute_workflow(const std::string& workflow_name, const CoffeeOrder& order) {
    return impl_->execute_workflow(workflow_name, order);
}

bool CoffeeWorkflowEngine::execute_workflow(const CoffeeWorkflow& workflow, const CoffeeOrder& order) {
    return impl_->execute_workflow(workflow, order);
}

bool CoffeeWorkflowEngine::stop_current_workflow() {
    return impl_->stop_current_workflow();
}

bool CoffeeWorkflowEngine::is_executing() const {
    return impl_->is_executing();
}

std::string CoffeeWorkflowEngine::get_current_step() const {
    return impl_->get_current_step();
}

void CoffeeWorkflowEngine::set_global_settings(const WorkflowSettings& settings) {
    impl_->set_global_settings(settings);
}

WorkflowSettings CoffeeWorkflowEngine::get_global_settings() const {
    return impl_->get_global_settings();
}

// 辅助函数实现
ExecutionType string_to_execution_type(const std::string& type_string) {
    if (type_string == "parallel") return ExecutionType::PARALLEL;
    return ExecutionType::SEQUENTIAL;
}

ActionType string_to_action_type(const std::string& type_string) {
    if (type_string == "wait") return ActionType::WAIT;
    if (type_string == "group") return ActionType::GROUP;
    return ActionType::ACTION;
}

ExecutorType string_to_executor_type(const std::string& executor_string) {
    if (executor_string == "left_arm") return ExecutorType::LEFT_ARM;
    if (executor_string == "right_arm") return ExecutorType::RIGHT_ARM;
    if (executor_string == "cup_dispenser") return ExecutorType::CUP_DISPENSER;
    if (executor_string == "coffee_machine") return ExecutorType::COFFEE_MACHINE;
    if (executor_string == "milk_container_cleaner") return ExecutorType::MILK_CONTAINER_CLEANER;
    return ExecutorType::LEFT_ARM;
}

std::string execution_type_to_string(ExecutionType type) {
    switch (type) {
        case ExecutionType::PARALLEL: return "parallel";
        case ExecutionType::SEQUENTIAL: return "sequential";
        default: return "sequential";
    }
}

std::string action_type_to_string(ActionType type) {
    switch (type) {
        case ActionType::ACTION: return "action";
        case ActionType::WAIT: return "wait";
        case ActionType::GROUP: return "group";
        default: return "action";
    }
}

std::string executor_type_to_string(ExecutorType executor) {
    switch (executor) {
        case ExecutorType::LEFT_ARM: return "left_arm";
        case ExecutorType::RIGHT_ARM: return "right_arm";
        case ExecutorType::CUP_DISPENSER: return "cup_dispenser";
        case ExecutorType::COFFEE_MACHINE: return "coffee_machine";
        case ExecutorType::MILK_CONTAINER_CLEANER: return "milk_container_cleaner";
        default: return "left_arm";
    }
}

} // namespace aubo
