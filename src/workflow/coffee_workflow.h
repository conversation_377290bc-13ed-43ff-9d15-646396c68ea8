/*
 * Copyright ©2015-2025 AUBO (Beijing) Robotics Technology Co., Ltd. All rights reserved.
 */

#ifndef AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H
#define AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H

#include <any>
#include <chrono>
#include <functional>
#include <map>
#include <memory>
#include <string>
#include <variant>
#include <vector>

#include "../core/coffee_order.h"

namespace aubo {

// 前向声明
class DeviceManager;

/**
 * @enum ExecutionType
 * @brief 执行类型枚举
 */
enum class ExecutionType {
    SEQUENTIAL,     ///< 顺序执行
    PARALLEL        ///< 并行执行
};



/**
 * @enum ExecutorType
 * @brief 执行器类型枚举
 */
enum class ExecutorType {
    LEFT_ARM,           ///< 左臂
    RIGHT_ARM,          ///< 右臂
    CUP_DISPENSER,      ///< 杯子分配器
    COFFEE_MACHINE,     ///< 咖啡机
    MILK_CONTAINER_CLEANER  ///< 牛奶容器清洁器
};

/**
 * @struct ActionParameters
 * @brief 动作参数结构
 */
struct ActionParameters {
    std::map<std::string, std::any> params;
    
    template<typename T>
    void set(const std::string& key, const T& value) {
        params[key] = value;
    }
    
    template<typename T>
    T get(const std::string& key, const T& default_value = T{}) const {
        auto it = params.find(key);
        if (it != params.end()) {
            try {
                return std::any_cast<T>(it->second);
            } catch (const std::bad_any_cast&) {
                return default_value;
            }
        }
        return default_value;
    }
    
    bool has(const std::string& key) const {
        return params.find(key) != params.end();
    }
};

// 前向声明
struct WorkflowItem;

/**
 * @struct WorkflowAction
 * @brief 基础动作项
 */
struct WorkflowAction {
    std::string name;                   ///< 动作名称（可选）
    ExecutorType executor;              ///< 执行器类型
    std::string operation;              ///< 操作名称
    ActionParameters parameters;        ///< 动作参数

    WorkflowAction() : executor(ExecutorType::LEFT_ARM) {}
};

/**
 * @struct WorkflowWait
 * @brief 等待项
 */
struct WorkflowWait {
    std::string name;                   ///< 等待名称（可选）
    int duration;                       ///< 等待时长(秒)

    WorkflowWait() : duration(0) {}
};

/**
 * @struct WorkflowGroup
 * @brief 执行组项
 */
struct WorkflowGroup {
    std::string name;                   ///< 组名称
    ExecutionType mode;                 ///< 执行模式：sequential/parallel
    std::vector<WorkflowItem> items;    ///< 子项列表

    WorkflowGroup() : mode(ExecutionType::SEQUENTIAL) {}
};

/**
 * @struct WorkflowItem
 * @brief 统一的工作流执行项（使用 variant 存储不同类型）
 */
using WorkflowItem = std::variant<WorkflowAction, WorkflowWait, WorkflowGroup>;

/**
 * @struct CoffeeWorkflow
 * @brief 咖啡制作工作流结构
 */
struct CoffeeWorkflow {
    std::string name;                   ///< 工作流名称
    std::string description;            ///< 工作流描述
    std::string coffee_type;            ///< 咖啡类型
    WorkflowItem root_steps;            ///< 根步骤组

    CoffeeWorkflow() {
        root_steps = WorkflowGroup{};
    }

    CoffeeWorkflow(const std::string& workflow_name, const std::string& workflow_description)
        : name(workflow_name), description(workflow_description) {
        root_steps = WorkflowGroup{};
    }
};

/**
 * @struct WorkflowContext
 * @brief 工作流执行上下文
 */
struct WorkflowContext {
    std::map<std::string, std::string> parameters;  ///< 执行参数

    WorkflowContext() = default;

    // 便利方法
    void set_parameter(const std::string& key, const std::string& value) {
        parameters[key] = value;
    }

    std::string get_parameter(const std::string& key, const std::string& default_value = "") const {
        auto it = parameters.find(key);
        return (it != parameters.end()) ? it->second : default_value;
    }

    bool has_parameter(const std::string& key) const {
        return parameters.find(key) != parameters.end();
    }
};

/**
 * @class CoffeeWorkflowEngine
 * @brief 咖啡制作工作流执行引擎
 */
class CoffeeWorkflowEngine {
public:
    /**
     * @brief 构造函数
     * @param device_manager 设备管理器实例
     */
    CoffeeWorkflowEngine(std::shared_ptr<DeviceManager> device_manager);
    
    /**
     * @brief 析构函数
     */
    ~CoffeeWorkflowEngine();
    
    /**
     * @brief 从工作流目录加载所有工作流
     * @param workflow_directory 工作流目录路径
     * @return 加载成功返回true
     */
    bool load_workflows_from_directory(const std::string& workflow_directory);

    /**
     * @brief 加载单个工作流文件
     * @param workflow_file_path 工作流文件路径
     * @return 加载成功返回true
     */
    bool load_single_workflow(const std::string& workflow_file_path);
    
    /**
     * @brief 获取可用的工作流列表
     * @return 工作流名称列表
     */
    std::vector<std::string> get_available_workflows() const;
    
    /**
     * @brief 获取指定工作流
     * @param workflow_name 工作流名称
     * @return 工作流结构，如果不存在返回空
     */
    CoffeeWorkflow get_workflow(const std::string& workflow_name) const;
    
    /**
     * @brief 执行工作流
     * @param workflow_name 工作流名称
     * @param context 执行上下文（用于参数传递）
     * @return 执行成功返回true
     */
    bool execute_workflow(const std::string& workflow_name, const WorkflowContext& context = WorkflowContext{});

    /**
     * @brief 执行自定义工作流
     * @param workflow 工作流结构
     * @param context 执行上下文（用于参数传递）
     * @return 执行成功返回true
     */
    bool execute_workflow(const CoffeeWorkflow& workflow, const WorkflowContext& context = WorkflowContext{});
    
    /**
     * @brief 停止当前执行的工作流
     * @return 停止成功返回true
     */
    bool stop_current_workflow();
    
    /**
     * @brief 获取当前执行状态
     * @return 当前是否正在执行工作流
     */
    bool is_executing() const;
    
    /**
     * @brief 获取当前执行的步骤信息
     * @return 当前步骤名称，如果未执行返回空字符串
     */
    std::string get_current_step() const;
    




private:
    class Impl;
    std::unique_ptr<Impl> impl_;
};

/**
 * @brief 从字符串解析执行类型
 * @param type_string 类型字符串
 * @return 执行类型
 */
ExecutionType string_to_execution_type(const std::string& type_string);



/**
 * @brief 从字符串解析执行器类型
 * @param executor_string 执行器字符串
 * @return 执行器类型
 */
ExecutorType string_to_executor_type(const std::string& executor_string);

/**
 * @brief 获取执行类型的字符串表示
 * @param type 执行类型
 * @return 字符串表示
 */
std::string execution_type_to_string(ExecutionType type);



/**
 * @brief 获取执行器类型的字符串表示
 * @param executor 执行器类型
 * @return 字符串表示
 */
std::string executor_type_to_string(ExecutorType executor);

} // namespace aubo

#endif // AUBO_COFFEE_SERVICE_COFFEE_WORKFLOW_H
