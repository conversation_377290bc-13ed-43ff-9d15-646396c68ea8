#include <iostream>
#include <fstream>
#include "src/workflow/coffee_workflow.h"
#include "nlohmann/json.hpp"

using json = nlohmann::json;
using namespace aubo;

int main() {
    try {
        // 读取新的配置文件
        std::ifstream file("share/config/workflows/cappuccino_heart.json");
        if (!file.is_open()) {
            std::cerr << "无法打开配置文件" << std::endl;
            return 1;
        }

        json workflow_json;
        file >> workflow_json;

        if (workflow_json.contains("workflow")) {
            const auto& workflow_data = workflow_json["workflow"];
            
            std::cout << "工作流名称: " << workflow_data["name"] << std::endl;
            std::cout << "工作流描述: " << workflow_data["description"] << std::endl;
            
            if (workflow_data.contains("steps")) {
                const auto& steps = workflow_data["steps"];
                std::cout << "步骤名称: " << steps["name"] << std::endl;
                std::cout << "执行模式: " << steps["mode"] << std::endl;
                
                if (steps.contains("items")) {
                    std::cout << "包含 " << steps["items"].size() << " 个子项" << std::endl;
                    
                    for (size_t i = 0; i < steps["items"].size(); ++i) {
                        const auto& item = steps["items"][i];
                        std::cout << "  项目 " << (i+1) << ": " << item["name"] 
                                  << " (类型: " << item["type"] 
                                  << ", 模式: " << item["mode"] << ")" << std::endl;
                    }
                }
            }
        }
        
        std::cout << "配置文件解析成功！" << std::endl;
        return 0;
        
    } catch (const std::exception& e) {
        std::cerr << "解析失败: " << e.what() << std::endl;
        return 1;
    }
}
